/* Modern Authentication Form Styles - SEO Compliant & Responsive */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --border-focus: #3b82f6;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Main Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    position: relative;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../img/bg.png') center/cover;
    opacity: 0.1;
    z-index: -1;
}

/* Form Card */
.auth-card {
    background: var(--card-background);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    display: block;
    border-radius: var(--radius-md);
}

.auth-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 400;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.form-label.required::after {
    content: ' *';
    color: var(--error-color);
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #ffffff;
    transition: all 0.2s ease-in-out;
    outline: none;
}

.form-input:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    border-color: var(--error-color);
}

.form-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* Input Icons */
.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1rem;
    pointer-events: none;
}

.input-group .form-input {
    padding-left: 2.75rem;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1rem;
    padding: 0;
    outline: none;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: var(--text-primary);
}

/* Error Messages */
.error-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message i {
    font-size: 0.75rem;
}

/* Success Messages */
.success-message {
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Alert Messages */
.alert {
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
    font-size: 0.875rem;
    line-height: 1.4;
}

.alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1d4ed8;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    border-radius: var(--radius-md);
    border: 2px solid transparent;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    outline: none;
    min-height: 48px;
    width: 100%;
}

.btn-primary {
    background-color: var(--primary-color);
    color: #ffffff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading Button */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Links */
.form-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.form-link:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Form Navigation */
.form-nav {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.form-nav p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.form-nav-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Remember Me Checkbox */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.checkbox-input {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

.checkbox-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

/* Responsive Design */
@media (max-width: 640px) {
    .auth-container {
        padding: 0.5rem;
    }
    
    .auth-card {
        padding: 1.5rem;
        margin: 0.5rem;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
    
    .form-nav-links {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1rem;
    }
    
    .auth-title {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: 0.875rem 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Form validation styles */
.form-input.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input.is-invalid {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.was-validated .form-input:valid {
    border-color: var(--success-color);
}

.was-validated .form-input:invalid {
    border-color: var(--error-color);
}

/* Form text helper */
.form-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    display: block;
    line-height: 1.4;
}

/* Focus management */
.form-input:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Smooth transitions */
.form-input,
.btn,
.alert {
    transition: all 0.2s ease-in-out;
}

/* Enhanced accessibility */
@media (prefers-reduced-motion: reduce) {
    .form-input,
    .btn,
    .alert,
    .loading-logo,
    .loading-spinner {
        transition: none;
        animation: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0f172a;
        --card-background: #1e293b;
        --text-primary: #f1f5f9;
        --text-secondary: #94a3b8;
        --border-color: #334155;
    }

    body {
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    }

    .auth-card {
        background: rgba(30, 41, 59, 0.95);
        border: 1px solid rgba(51, 65, 85, 0.5);
    }

    .form-input {
        background-color: #334155;
        color: var(--text-primary);
        border-color: var(--border-color);
    }

    .form-input::placeholder {
        color: var(--text-secondary);
    }
}

/* Print styles */
@media print {
    .auth-container {
        background: none;
    }

    .auth-card {
        box-shadow: none;
        border: 1px solid #000;
    }

    .loading-screen {
        display: none !important;
    }

    .btn {
        background: none !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
}

/* SEO and Performance optimizations */
.auth-logo,
.loading-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Improved focus indicators for better accessibility */
.form-link:focus,
.btn:focus,
.checkbox-input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Enhanced mobile experience */
@media (max-width: 480px) {
    .auth-container {
        min-height: 100vh;
        align-items: stretch;
    }

    .auth-card {
        border-radius: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .form-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .auth-logo,
    .loading-logo {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Animation performance optimization */
.loading-spinner,
.loading-logo {
    will-change: transform;
    transform: translateZ(0);
}

/* Improved button states */
.btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Enhanced error states */
.error-message {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success states */
.success-message {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
