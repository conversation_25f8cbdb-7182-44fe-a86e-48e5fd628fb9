

<?php $__env->startSection('title', 'Đăng nhập - Shop Thời Trang'); ?>
<?php $__env->startSection('description', 'Đăng nhập vào tài khoản Shop Thời Trang để trải nghiệm mua sắm với nhiều ưu đãi độc quyền và dịch vụ chăm sóc khách hàng tận tâm.'); ?>
<?php $__env->startSection('keywords', 'đăng nhập, login, shop thời trang, tài khoản, mua sắm online'); ?>

<?php $__env->startSection('content'); ?>
<!-- Loading Screen -->
<div id="loadingScreen" class="loading-screen">
    <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Logo" class="loading-logo">
    <div class="loading-spinner"></div>
</div>

<main class="auth-container" role="main">
    <article class="auth-card">
        <!-- Header Section -->
        <header class="auth-header">
            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Logo" class="auth-logo">
            <h1 class="auth-title">Đăng nhập</h1>
            <p class="auth-subtitle">Chào mừng bạn quay trở lại!</p>
        </header>

        <!-- Alert Messages -->
        <?php if(Session::has('message')): ?>
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('message')); ?>

            </div>
            <?php Session::put('message', null); ?>
        <?php endif; ?>

        <?php if(Session::has('success')): ?>
            <div class="alert alert-success" role="alert">
                <i class="fa fa-check-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('success')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form action="<?php echo e(route('user.cus_login')); ?>" method="post" id="loginForm" novalidate>
            <?php echo csrf_field(); ?>

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label required">Địa chỉ email</label>
                <div class="input-group">
                    <i class="fa fa-envelope input-icon" aria-hidden="true"></i>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập địa chỉ email của bạn"
                        value="<?php echo e(old('email')); ?>"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                </div>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="email-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label required">Mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập mật khẩu của bạn"
                        required
                        autocomplete="current-password"
                        aria-describedby="password-error"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')" aria-label="Hiển thị/Ẩn mật khẩu">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="password-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Remember Me -->
            <div class="checkbox-group">
                <input type="checkbox" id="remember" name="remember" class="checkbox-input" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                <label for="remember" class="checkbox-label">Ghi nhớ đăng nhập</label>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary" id="loginBtn">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                Đăng nhập
            </button>

            <!-- Navigation Links -->
            <nav class="form-nav" role="navigation">
                <div class="form-nav-links">
                    <a href="#" class="form-link" onclick="showForgotPasswordAlert(); return false;">
                        <i class="fa fa-question-circle" aria-hidden="true"></i>
                        Quên mật khẩu?
                    </a>
                    <a href="<?php echo e(route('home.index')); ?>" class="form-link">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Trang chủ
                    </a>
                </div>

                <p style="margin-top: 1rem;">
                    Bạn chưa có tài khoản?
                    <a href="<?php echo e(route('user.cus_register')); ?>" class="form-link">
                        <strong>Đăng ký ngay</strong>
                    </a>
                </p>
            </nav>
        </form>
    </article>
</main>

    
    <script>
        function validate() {
            var $valid = true;
            document.getElementById("user_info").innerHTML = "";
            document.getElementById("password_info").innerHTML = "";

            var userName = document.getElementById("user_name").value;
            var password = document.getElementById("password").value;

            if (userName == "") {
                document.getElementById("user_info").innerHTML = "required";
                $valid = false;
            }
            if (password == "") {
                document.getElementById("password_info").innerHTML = "required";
                $valid = false;
            }

            return $valid;
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/cus_login.blade.php ENDPATH**/ ?>