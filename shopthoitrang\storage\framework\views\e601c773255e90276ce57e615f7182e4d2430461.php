

<?php $__env->startSection('title', 'Đăng ký tài khoản - Shop Thời Trang'); ?>
<?php $__env->startSection('description', 'Tạo tài khoản mới tại Shop Thời Trang để nhận được những ưu đãi độ<PERSON> quyề<PERSON>, theo dõi đơn hàng và trải nghiệm mua sắm cá nhân hóa.'); ?>
<?php $__env->startSection('keywords', 'đăng ký, tạo tài khoản, register, shop thời trang, thành viên mới'); ?>

<?php $__env->startSection('content'); ?>
<!-- Loading Screen -->
<div id="loadingScreen" class="loading-screen">
    <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Logo" class="loading-logo">
    <div class="loading-spinner"></div>
</div>

<main class="auth-container" role="main">
    <article class="auth-card">
        <!-- Header Section -->
        <header class="auth-header">
            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang Logo" class="auth-logo">
            <h1 class="auth-title">Đăng ký tài khoản</h1>
            <p class="auth-subtitle">Tham gia cộng đồng thời trang của chúng tôi!</p>
        </header>

        <!-- Alert Messages -->
        <?php if(Session::has('message')): ?>
            <div class="alert alert-info" role="alert">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('message')); ?>

            </div>
            <?php Session::put('message', null); ?>
        <?php endif; ?>

        <?php if(Session::has('success')): ?>
            <div class="alert alert-success" role="alert">
                <i class="fa fa-check-circle" aria-hidden="true"></i>
                <?php echo e(Session::get('success')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Registration Form -->
        <form action="<?php echo e(route('user.cus_register')); ?>" method="post" id="registerForm" novalidate>
            <?php echo csrf_field(); ?>

            <!-- Full Name Field -->
            <div class="form-group">
                <label for="name" class="form-label required">Họ và tên</label>
                <div class="input-group">
                    <i class="fa fa-user input-icon" aria-hidden="true"></i>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập họ và tên đầy đủ"
                        value="<?php echo e(old('name')); ?>"
                        required
                        autocomplete="name"
                        aria-describedby="name-error"
                    >
                </div>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="name-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label required">Địa chỉ email</label>
                <div class="input-group">
                    <i class="fa fa-envelope input-icon" aria-hidden="true"></i>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập địa chỉ email của bạn"
                        value="<?php echo e(old('email')); ?>"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                </div>
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="email-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label required">Mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Tạo mật khẩu mạnh (tối thiểu 8 ký tự)"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-error password-help"
                        minlength="8"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')" aria-label="Hiển thị/Ẩn mật khẩu">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <small id="password-help" class="form-text" style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem; display: block;">Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số</small>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="password-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Confirm Password Field -->
            <div class="form-group">
                <label for="password_confirmation" class="form-label required">Xác nhận mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        class="form-input <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập lại mật khẩu để xác nhận"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-confirmation-error"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')" aria-label="Hiển thị/Ẩn mật khẩu xác nhận">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="password-confirmation-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Phone Field -->
            <div class="form-group">
                <label for="phone" class="form-label required">Số điện thoại</label>
                <div class="input-group">
                    <i class="fa fa-phone input-icon" aria-hidden="true"></i>
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        class="form-input <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập số điện thoại (VD: 0901234567)"
                        value="<?php echo e(old('phone')); ?>"
                        required
                        autocomplete="tel"
                        aria-describedby="phone-error"
                        pattern="[0-9]{10,11}"
                    >
                </div>
                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="phone-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Address Field -->
            <div class="form-group">
                <label for="address" class="form-label required">Địa chỉ</label>
                <div class="input-group">
                    <i class="fa fa-map-marker input-icon" aria-hidden="true"></i>
                    <input
                        type="text"
                        id="address"
                        name="address"
                        class="form-input <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Nhập địa chỉ đầy đủ của bạn"
                        value="<?php echo e(old('address')); ?>"
                        required
                        autocomplete="street-address"
                        aria-describedby="address-error"
                    >
                </div>
                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error-message" id="address-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <?php echo e($message); ?>

                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Terms and Conditions -->
            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" class="checkbox-input" required>
                <label for="terms" class="checkbox-label">
                    Tôi đồng ý với
                    <a href="#" class="form-link" onclick="showTermsModal(); return false;">Điều khoản sử dụng</a>
                    và
                    <a href="#" class="form-link" onclick="showPrivacyModal(); return false;">Chính sách bảo mật</a>
                </label>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary" id="registerBtn">
                <i class="fa fa-user-plus" aria-hidden="true"></i>
                Tạo tài khoản
            </button>

            <!-- Navigation Links -->
            <nav class="form-nav" role="navigation">
                <div class="form-nav-links">
                    <a href="<?php echo e(route('home.index')); ?>" class="form-link">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Trang chủ
                    </a>
                </div>

                <p style="margin-top: 1rem;">
                    Đã có tài khoản?
                    <a href="<?php echo e(route('user.cus_login')); ?>" class="form-link">
                        <strong>Đăng nhập ngay</strong>
                    </a>
                </p>
            </nav>
        </form>
    </article>
</main>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/cus_register.blade.php ENDPATH**/ ?>