<?php $__env->startSection('title', 'Products'); ?>
<?php $__env->startSection('page_title', 'Products Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Products List</h3>
                    <div class="card-tools">
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Product
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Manufacturer</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($product->id_product); ?></td>
                                    <td>
                                        <?php if($product->image_address_product): ?>
                                            <img src="<?php echo e(asset('uploads/productimage/' . $product->image_address_product)); ?>" 
                                                 alt="<?php echo e($product->name_product); ?>" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e(Str::limit($product->name_product, 30)); ?></strong>
                                        <?php if($product->slug): ?>
                                            <br><small class="text-muted"><?php echo e($product->slug); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($product->category->name_category ?? 'N/A'); ?></td>
                                    <td><?php echo e($product->manufacturer->name_manufacturer ?? 'N/A'); ?></td>
                                    <td>
                                        <strong><?php echo e(number_format($product->price_product, 0, ',', '.')); ?>₫</strong>
                                        <?php if($product->compare_price && $product->compare_price > $product->price_product): ?>
                                            <br><small class="text-muted"><del><?php echo e(number_format($product->compare_price, 0, ',', '.')); ?>₫</del></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo e($product->quantity_product > 0 ? 'success' : 'danger'); ?>">
                                            <?php echo e($product->quantity_product); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if(($product->status ?? 'active') === 'active'): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info">View</a>
                                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                                        <a href="#" class="btn btn-sm btn-danger">Delete</a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center">No products found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php if($products->hasPages()): ?>
                <div class="card-footer">
                    <?php echo e($products->links()); ?>

                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.simple', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/products/simple.blade.php ENDPATH**/ ?>