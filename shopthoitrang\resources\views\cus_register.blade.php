@extends('layout.app')

@section('title', 'Đăng ký tài khoản - Shop Thời Trang')
@section('description', 'Tạo tài khoản mới tại Shop Thời Trang để nhận được những ưu đãi độ<PERSON>uy<PERSON>, theo dõi đơn hàng và trải nghiệm mua sắm cá nhân hóa.')
@section('keywords', 'đăng ký, tạo tài kho<PERSON>n, register, shop thời trang, thành viên mới')

@section('content')
<!-- Loading Screen -->
<div id="loadingScreen" class="loading-screen">
    <img src="{{ asset('img/logo.png') }}" alt="Shop Thời Trang Logo" class="loading-logo">
    <div class="loading-spinner"></div>
</div>

<main class="auth-container" role="main">
    <article class="auth-card">
        <!-- Header Section -->
        <header class="auth-header">
            <img src="{{ asset('img/logo.png') }}" alt="Shop Thời Trang Logo" class="auth-logo">
            <h1 class="auth-title">Đăng ký tài khoản</h1>
            <p class="auth-subtitle">Tham gia cộng đồng thời trang của chúng tôi!</p>
        </header>

        <!-- Alert Messages -->
        @if (Session::has('message'))
            <div class="alert alert-info" role="alert">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                {{ Session::get('message') }}
            </div>
            @php Session::put('message', null); @endphp
        @endif

        @if (Session::has('success'))
            <div class="alert alert-success" role="alert">
                <i class="fa fa-check-circle" aria-hidden="true"></i>
                {{ Session::get('success') }}
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Registration Form -->
        <form action="{{ route('user.cus_register') }}" method="post" id="registerForm" novalidate>
            @csrf

            <!-- Full Name Field -->
            <div class="form-group">
                <label for="name" class="form-label required">Họ và tên</label>
                <div class="input-group">
                    <i class="fa fa-user input-icon" aria-hidden="true"></i>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input @error('name') is-invalid @enderror"
                        placeholder="Nhập họ và tên đầy đủ"
                        value="{{ old('name') }}"
                        required
                        autocomplete="name"
                        aria-describedby="name-error"
                    >
                </div>
                @error('name')
                    <div class="error-message" id="name-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label required">Địa chỉ email</label>
                <div class="input-group">
                    <i class="fa fa-envelope input-icon" aria-hidden="true"></i>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input @error('email') is-invalid @enderror"
                        placeholder="Nhập địa chỉ email của bạn"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                </div>
                @error('email')
                    <div class="error-message" id="email-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label required">Mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input @error('password') is-invalid @enderror"
                        placeholder="Tạo mật khẩu mạnh (tối thiểu 8 ký tự)"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-error password-help"
                        minlength="8"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')" aria-label="Hiển thị/Ẩn mật khẩu">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                <small id="password-help" class="form-text" style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem; display: block;">Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số</small>
                @error('password')
                    <div class="error-message" id="password-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Confirm Password Field -->
            <div class="form-group">
                <label for="password_confirmation" class="form-label required">Xác nhận mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        class="form-input @error('password_confirmation') is-invalid @enderror"
                        placeholder="Nhập lại mật khẩu để xác nhận"
                        required
                        autocomplete="new-password"
                        aria-describedby="password-confirmation-error"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')" aria-label="Hiển thị/Ẩn mật khẩu xác nhận">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                @error('password_confirmation')
                    <div class="error-message" id="password-confirmation-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Phone Field -->
            <div class="form-group">
                <label for="phone" class="form-label required">Số điện thoại</label>
                <div class="input-group">
                    <i class="fa fa-phone input-icon" aria-hidden="true"></i>
                    <input
                        type="tel"
                        id="phone"
                        name="phone"
                        class="form-input @error('phone') is-invalid @enderror"
                        placeholder="Nhập số điện thoại (VD: 0901234567)"
                        value="{{ old('phone') }}"
                        required
                        autocomplete="tel"
                        aria-describedby="phone-error"
                        pattern="[0-9]{10,11}"
                    >
                </div>
                @error('phone')
                    <div class="error-message" id="phone-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Address Field -->
            <div class="form-group">
                <label for="address" class="form-label required">Địa chỉ</label>
                <div class="input-group">
                    <i class="fa fa-map-marker input-icon" aria-hidden="true"></i>
                    <input
                        type="text"
                        id="address"
                        name="address"
                        class="form-input @error('address') is-invalid @enderror"
                        placeholder="Nhập địa chỉ đầy đủ của bạn"
                        value="{{ old('address') }}"
                        required
                        autocomplete="street-address"
                        aria-describedby="address-error"
                    >
                </div>
                @error('address')
                    <div class="error-message" id="address-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Terms and Conditions -->
            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" class="checkbox-input" required>
                <label for="terms" class="checkbox-label">
                    Tôi đồng ý với
                    <a href="#" class="form-link" onclick="showTermsModal(); return false;">Điều khoản sử dụng</a>
                    và
                    <a href="#" class="form-link" onclick="showPrivacyModal(); return false;">Chính sách bảo mật</a>
                </label>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary" id="registerBtn">
                <i class="fa fa-user-plus" aria-hidden="true"></i>
                Tạo tài khoản
            </button>

            <!-- Navigation Links -->
            <nav class="form-nav" role="navigation">
                <div class="form-nav-links">
                    <a href="{{ route('home.index') }}" class="form-link">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Trang chủ
                    </a>
                </div>

                <p style="margin-top: 1rem;">
                    Đã có tài khoản?
                    <a href="{{ route('user.cus_login') }}" class="form-link">
                        <strong>Đăng nhập ngay</strong>
                    </a>
                </p>
            </nav>
        </form>
    </article>
</main>

@endsection
