/**
 * Authentication Form Enhancement Script
 * Provides modern UX features for login and registration forms
 * SEO-compliant and accessibility-focused
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        loadingDelay: 1000,
        alertDismissDelay: 5000,
        animationDuration: 300,
        passwordMinLength: 8
    };

    // Utility functions
    const utils = {
        // Debounce function for performance
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        },

        // Smooth scroll to element
        scrollToElement: function(element, offset = 0) {
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    };

    // Loading Screen Management
    const LoadingManager = {
        init: function() {
            this.loadingScreen = document.getElementById('loadingScreen');
            this.hideLoadingScreen();
        },

        showLoadingScreen: function() {
            if (this.loadingScreen) {
                this.loadingScreen.style.display = 'flex';
                this.loadingScreen.style.opacity = '1';
                document.body.style.overflow = 'hidden';
            }
        },

        hideLoadingScreen: function() {
            if (this.loadingScreen) {
                setTimeout(() => {
                    this.loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        this.loadingScreen.style.display = 'none';
                        document.body.style.overflow = '';
                    }, CONFIG.animationDuration);
                }, CONFIG.loadingDelay);
            }
        }
    };

    // Password Management
    const PasswordManager = {
        init: function() {
            this.setupPasswordToggles();
            this.setupPasswordValidation();
        },

        setupPasswordToggles: function() {
            const toggleButtons = document.querySelectorAll('.password-toggle');
            toggleButtons.forEach(button => {
                button.addEventListener('click', this.togglePassword.bind(this));
            });
        },

        togglePassword: function(event) {
            const button = event.currentTarget;
            const passwordField = button.previousElementSibling;
            const icon = button.querySelector('i');

            if (passwordField && passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                button.setAttribute('aria-label', 'Ẩn mật khẩu');
            } else if (passwordField) {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                button.setAttribute('aria-label', 'Hiển thị mật khẩu');
            }
        },

        setupPasswordValidation: function() {
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('password_confirmation');

            if (passwordField && confirmPasswordField) {
                const validatePasswordMatch = () => {
                    if (confirmPasswordField.value && confirmPasswordField.value !== passwordField.value) {
                        confirmPasswordField.setCustomValidity('Mật khẩu xác nhận không khớp');
                        this.showFieldError(confirmPasswordField, 'Mật khẩu xác nhận không khớp');
                    } else {
                        confirmPasswordField.setCustomValidity('');
                        this.clearFieldError(confirmPasswordField);
                    }
                };

                passwordField.addEventListener('input', utils.debounce(validatePasswordMatch, 300));
                confirmPasswordField.addEventListener('input', utils.debounce(validatePasswordMatch, 300));
            }

            // Password strength indicator
            if (passwordField) {
                passwordField.addEventListener('input', utils.debounce(this.checkPasswordStrength.bind(this), 300));
            }
        },

        checkPasswordStrength: function(event) {
            const password = event.target.value;
            const strengthIndicator = document.getElementById('password-strength');
            
            if (!strengthIndicator) return;

            let strength = 0;
            let feedback = [];

            // Length check
            if (password.length >= CONFIG.passwordMinLength) strength++;
            else feedback.push('Ít nhất 8 ký tự');

            // Uppercase check
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('Chữ hoa');

            // Lowercase check
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('Chữ thường');

            // Number check
            if (/\d/.test(password)) strength++;
            else feedback.push('Số');

            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            else feedback.push('Ký tự đặc biệt');

            this.updatePasswordStrengthUI(strengthIndicator, strength, feedback);
        },

        updatePasswordStrengthUI: function(indicator, strength, feedback) {
            const strengthLevels = ['Rất yếu', 'Yếu', 'Trung bình', 'Mạnh', 'Rất mạnh'];
            const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#16a34a'];

            indicator.textContent = `Độ mạnh: ${strengthLevels[strength] || 'Rất yếu'}`;
            indicator.style.color = strengthColors[strength] || strengthColors[0];

            if (feedback.length > 0) {
                indicator.textContent += ` (Cần: ${feedback.join(', ')})`;
            }
        },

        showFieldError: function(field, message) {
            let errorElement = field.parentNode.querySelector('.error-message');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'error-message';
                errorElement.setAttribute('role', 'alert');
                field.parentNode.appendChild(errorElement);
            }
            errorElement.innerHTML = `<i class="fa fa-exclamation-triangle" aria-hidden="true"></i> ${message}`;
            field.classList.add('is-invalid');
        },

        clearFieldError: function(field) {
            const errorElement = field.parentNode.querySelector('.error-message');
            if (errorElement) {
                errorElement.remove();
            }
            field.classList.remove('is-invalid');
        }
    };

    // Form Validation Manager
    const FormValidator = {
        init: function() {
            this.setupFormValidation();
            this.setupRealTimeValidation();
        },

        setupFormValidation: function() {
            const forms = document.querySelectorAll('form[novalidate]');
            
            forms.forEach(form => {
                form.addEventListener('submit', this.handleFormSubmit.bind(this));
            });
        },

        handleFormSubmit: function(event) {
            const form = event.target;
            
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Focus first invalid field
                const firstInvalidField = form.querySelector(':invalid');
                if (firstInvalidField) {
                    firstInvalidField.focus();
                    utils.scrollToElement(firstInvalidField, 100);
                }
            } else {
                // Show loading state
                this.showFormLoading(form);
                LoadingManager.showLoadingScreen();
            }
            
            form.classList.add('was-validated');
        },

        showFormLoading: function(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.classList.add('btn-loading');
                submitBtn.disabled = true;
                
                // Store original text
                submitBtn.dataset.originalText = submitBtn.textContent;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin" aria-hidden="true"></i> Đang xử lý...';
            }
        },

        setupRealTimeValidation: function() {
            const inputs = document.querySelectorAll('.form-input');
            
            inputs.forEach(input => {
                input.addEventListener('blur', this.validateField.bind(this));
                input.addEventListener('input', this.clearValidationOnInput.bind(this));
            });
        },

        validateField: function(event) {
            const field = event.target;
            
            if (field.checkValidity()) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            } else {
                field.classList.remove('is-valid');
                field.classList.add('is-invalid');
            }
        },

        clearValidationOnInput: function(event) {
            const field = event.target;
            
            if (field.classList.contains('is-invalid') && field.checkValidity()) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }
    };

    // Alert Manager
    const AlertManager = {
        init: function() {
            this.setupAutoDismiss();
            this.setupKeyboardDismiss();
        },

        setupAutoDismiss: function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    this.dismissAlert(alert);
                }, CONFIG.alertDismissDelay);
            });
        },

        setupKeyboardDismiss: function() {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    const alerts = document.querySelectorAll('.alert');
                    alerts.forEach(alert => this.dismissAlert(alert));
                }
            });
        },

        dismissAlert: function(alert) {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, CONFIG.animationDuration);
        }
    };

    // Modal Functions (Global)
    window.showForgotPasswordAlert = function() {
        alert('Chức năng quên mật khẩu đang được phát triển. Vui lòng liên hệ admin để được hỗ trợ.');
    };

    window.showTermsModal = function() {
        alert('Điều khoản sử dụng:\n\n1. Bạn phải cung cấp thông tin chính xác khi đăng ký.\n2. Không được sử dụng tài khoản cho mục đích bất hợp pháp.\n3. Chúng tôi có quyền khóa tài khoản vi phạm quy định.\n4. Thông tin cá nhân sẽ được bảo mật theo chính sách của chúng tôi.');
    };

    window.showPrivacyModal = function() {
        alert('Chính sách bảo mật:\n\n1. Thông tin cá nhân của bạn được mã hóa và bảo vệ.\n2. Chúng tôi không chia sẻ thông tin với bên thứ ba.\n3. Cookies được sử dụng để cải thiện trải nghiệm người dùng.\n4. Bạn có quyền yêu cầu xóa dữ liệu cá nhân.');
    };

    // Initialize everything when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        LoadingManager.init();
        PasswordManager.init();
        FormValidator.init();
        AlertManager.init();
    });

})();
