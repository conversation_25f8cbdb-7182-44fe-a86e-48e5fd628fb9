<?php $__env->startSection('title', 'Manufacturers'); ?>
<?php $__env->startSection('page_title', 'Manufacturers Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Manufacturers List</h3>
                    <div class="card-tools">
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Manufacturer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Logo</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Website</th>
                                    <th>Products</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($manufacturer->id_manufacturer); ?></td>
                                    <td>
                                        <?php if($manufacturer->logo): ?>
                                            <img src="<?php echo e(asset('storage/' . $manufacturer->logo)); ?>" 
                                                 alt="<?php echo e($manufacturer->name_manufacturer); ?>" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        <?php elseif($manufacturer->image_manufacturer): ?>
                                            <img src="<?php echo e(asset('uploads/manufacturerimage/' . $manufacturer->image_manufacturer)); ?>" 
                                                 alt="<?php echo e($manufacturer->name_manufacturer); ?>" 
                                                 style="width: 50px; height: 50px; object-fit: cover;" class="img-thumbnail">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-industry text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($manufacturer->name_manufacturer); ?></strong>
                                        <?php if($manufacturer->slug): ?>
                                            <br><small class="text-muted"><?php echo e($manufacturer->slug); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($manufacturer->email ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if($manufacturer->website_url): ?>
                                            <a href="<?php echo e($manufacturer->website_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary"><?php echo e($manufacturer->product_count ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <?php if($manufacturer->is_active ?? true): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info">View</a>
                                        <a href="#" class="btn btn-sm btn-warning">Edit</a>
                                        <a href="#" class="btn btn-sm btn-danger">Delete</a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center">No manufacturers found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php if($manufacturers->hasPages()): ?>
                <div class="card-footer">
                    <?php echo e($manufacturers->links()); ?>

                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.simple', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/manufacturers/simple.blade.php ENDPATH**/ ?>