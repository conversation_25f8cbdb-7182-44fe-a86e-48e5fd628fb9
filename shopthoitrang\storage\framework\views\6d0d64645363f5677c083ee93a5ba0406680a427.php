<?php $__env->startSection('title', 'Quản lý Sản phẩm'); ?>
<?php $__env->startSection('page_title', 'Quản lý Sản phẩm'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Sản phẩm</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-box mr-1"></i>
                        Danh sách sản phẩm
                    </h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Thêm sản phẩm mới
                        </a>
                    </div>
                </div>

                <!-- Advanced Search and Filter -->
                <div class="card-body border-bottom">
                    <form method="GET" action="<?php echo e(route('admin.products.index')); ?>" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="search">Tìm kiếm:</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="<?php echo e(request('search')); ?>" placeholder="Tên sản phẩm, mô tả...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="category_id">Danh mục:</label>
                                <select name="category_id" id="category_id" class="form-control">
                                    <option value="">Tất cả</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id_category); ?>" <?php echo e(request('category_id') == $category->id_category ? 'selected' : ''); ?>>
                                            <?php echo e($category->name_category); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="manufacturer_id">Thương hiệu:</label>
                                <select name="manufacturer_id" id="manufacturer_id" class="form-control">
                                    <option value="">Tất cả</option>
                                    <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($manufacturer->id_manufacturer); ?>" <?php echo e(request('manufacturer_id') == $manufacturer->id_manufacturer ? 'selected' : ''); ?>>
                                            <?php echo e($manufacturer->name_manufacturer); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Trạng thái:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                                    <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                                    <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Nháp</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-info mr-2">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Additional Filters -->
                    <div class="row mt-3">
                        <div class="col-md-2">
                            <select name="stock_status" class="form-control" onchange="this.form.submit()">
                                <option value="">Tình trạng kho</option>
                                <option value="in_stock" <?php echo e(request('stock_status') === 'in_stock' ? 'selected' : ''); ?>>Còn hàng</option>
                                <option value="low_stock" <?php echo e(request('stock_status') === 'low_stock' ? 'selected' : ''); ?>>Sắp hết</option>
                                <option value="out_of_stock" <?php echo e(request('stock_status') === 'out_of_stock' ? 'selected' : ''); ?>>Hết hàng</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="featured" class="form-control" onchange="this.form.submit()">
                                <option value="">Nổi bật</option>
                                <option value="yes" <?php echo e(request('featured') === 'yes' ? 'selected' : ''); ?>>Có</option>
                                <option value="no" <?php echo e(request('featured') === 'no' ? 'selected' : ''); ?>>Không</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="number" name="price_min" class="form-control" placeholder="Giá từ" value="<?php echo e(request('price_min')); ?>">
                        </div>
                        <div class="col-md-2">
                            <input type="number" name="price_max" class="form-control" placeholder="Giá đến" value="<?php echo e(request('price_max')); ?>">
                        </div>
                        <div class="col-md-2">
                            <select name="sort_by" class="form-control" onchange="this.form.submit()">
                                <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Ngày tạo</option>
                                <option value="name_product" <?php echo e(request('sort_by') === 'name_product' ? 'selected' : ''); ?>>Tên sản phẩm</option>
                                <option value="price_product" <?php echo e(request('sort_by') === 'price_product' ? 'selected' : ''); ?>>Giá</option>
                                <option value="quantity_product" <?php echo e(request('sort_by') === 'quantity_product' ? 'selected' : ''); ?>>Số lượng</option>
                                <option value="view_count" <?php echo e(request('sort_by') === 'view_count' ? 'selected' : ''); ?>>Lượt xem</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="sort_order" class="form-control" onchange="this.form.submit()">
                                <option value="desc" <?php echo e(request('sort_order') === 'desc' ? 'selected' : ''); ?>>Giảm dần</option>
                                <option value="asc" <?php echo e(request('sort_order') === 'asc' ? 'selected' : ''); ?>>Tăng dần</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50px">#</th>
                                    <th>Hình ảnh</th>
                                    <th>Sản phẩm</th>
                                    <th>Danh mục</th>
                                    <th>Thương hiệu</th>
                                    <th>Giá</th>
                                    <th>Kho</th>
                                    <th>Trạng thái</th>
                                    <th>Nổi bật</th>
                                    <th>Lượt xem</th>
                                    <th style="width: 150px">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($product->id_product); ?></td>
                                    <td>
                                        <?php if($product->image_address_product): ?>
                                            <img src="<?php echo e(asset('uploads/productimage/' . $product->image_address_product)); ?>" 
                                                 alt="<?php echo e($product->name_product); ?>" 
                                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                        <?php elseif($product->images->count() > 0): ?>
                                            <img src="<?php echo e(asset('storage/' . $product->images->first()->image_path)); ?>" 
                                                 alt="<?php echo e($product->name_product); ?>" 
                                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 60px; height: 60px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e(Str::limit($product->name_product, 40)); ?></strong>
                                            <?php if($product->slug): ?>
                                                <br><small class="text-muted"><?php echo e($product->slug); ?></small>
                                            <?php endif; ?>
                                            <?php if($product->short_description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($product->short_description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo e($product->category->name_category ?? 'N/A'); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary"><?php echo e($product->manufacturer->name_manufacturer ?? 'N/A'); ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e(number_format($product->price_product, 0, ',', '.')); ?>₫</strong>
                                            <?php if($product->compare_price && $product->compare_price > $product->price_product): ?>
                                                <br><small class="text-muted"><del><?php echo e(number_format($product->compare_price, 0, ',', '.')); ?>₫</del></small>
                                                <span class="badge badge-danger">-<?php echo e(round((($product->compare_price - $product->price_product) / $product->compare_price) * 100)); ?>%</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                            $stockClass = 'success';
                                            if ($product->quantity_product == 0) {
                                                $stockClass = 'danger';
                                            } elseif ($product->min_stock_level && $product->quantity_product <= $product->min_stock_level) {
                                                $stockClass = 'warning';
                                            }
                                        ?>
                                        <span class="badge badge-<?php echo e($stockClass); ?>"><?php echo e($product->quantity_product); ?></span>
                                        <?php if($product->track_inventory && $product->min_stock_level && $product->quantity_product <= $product->min_stock_level): ?>
                                            <br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Sắp hết</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input status-toggle" 
                                                   id="status-<?php echo e($product->id_product); ?>"
                                                   data-id="<?php echo e($product->id_product); ?>"
                                                   <?php echo e($product->status === 'active' ? 'checked' : ''); ?>>
                                            <label class="custom-control-label" for="status-<?php echo e($product->id_product); ?>"></label>
                                        </div>
                                        <small class="text-muted"><?php echo e(ucfirst($product->status ?? 'draft')); ?></small>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input featured-toggle" 
                                                   id="featured-<?php echo e($product->id_product); ?>"
                                                   data-id="<?php echo e($product->id_product); ?>"
                                                   <?php echo e($product->is_featured ? 'checked' : ''); ?>>
                                            <label class="custom-control-label" for="featured-<?php echo e($product->id_product); ?>"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light"><?php echo e(number_format($product->view_count ?? 0)); ?></span>
                                        <?php if($product->reviews_count > 0): ?>
                                            <br><small class="text-muted"><?php echo e($product->reviews_count); ?> đánh giá</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.products.show', $product->id_product)); ?>" 
                                               class="btn btn-info btn-sm" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.products.edit', $product->id_product)); ?>" 
                                               class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" 
                                                    onclick="confirmDelete('<?php echo e(route('admin.products.destroy', $product->id_product)); ?>', 'Bạn có chắc chắn muốn xóa sản phẩm này?')"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-box-open fa-3x mb-3"></i>
                                            <p>Không có sản phẩm nào được tìm thấy.</p>
                                            <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Thêm sản phẩm đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if($products->hasPages()): ?>
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                Hiển thị <?php echo e($products->firstItem()); ?> đến <?php echo e($products->lastItem()); ?> 
                                trong tổng số <?php echo e($products->total()); ?> sản phẩm
                            </small>
                        </div>
                        <div class="col-md-6">
                            <?php echo e($products->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Toggle status
    $('.status-toggle').on('change', function() {
        const productId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/products/${productId}/toggle-status`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Có lỗi xảy ra');
                    // Revert toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Có lỗi xảy ra');
                // Revert toggle
                $(this).prop('checked', !isActive);
            }
        });
    });

    // Toggle featured
    $('.featured-toggle').on('change', function() {
        const productId = $(this).data('id');
        const isFeatured = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/products/${productId}/toggle-featured`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Có lỗi xảy ra');
                    // Revert toggle
                    $(this).prop('checked', !isFeatured);
                }
            },
            error: function() {
                toastr.error('Có lỗi xảy ra');
                // Revert toggle
                $(this).prop('checked', !isFeatured);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/products/index.blade.php ENDPATH**/ ?>