@extends('layout.app')

@section('title', 'Đăng nhập - Shop Thời Trang')
@section('description', 'Đăng nhập vào tài khoản Shop Thời Trang để trải nghiệm mua sắm với nhiều ưu đãi độc quyền và dịch vụ chăm sóc khách hàng tận tâm.')
@section('keywords', 'đăng nhập, login, shop thời trang, tài kho<PERSON>n, mua sắm online')

@section('content')
<!-- Loading Screen -->
<div id="loadingScreen" class="loading-screen">
    <img src="{{ asset('img/logo.png') }}" alt="Shop Thời Trang Logo" class="loading-logo">
    <div class="loading-spinner"></div>
</div>

<main class="auth-container" role="main">
    <article class="auth-card">
        <!-- Header Section -->
        <header class="auth-header">
            <img src="{{ asset('img/logo.png') }}" alt="Shop Thời Trang Logo" class="auth-logo">
            <h1 class="auth-title">Đăng nhập</h1>
            <p class="auth-subtitle">Chào mừng bạn quay trở lại!</p>
        </header>

        <!-- Alert Messages -->
        @if (Session::has('message'))
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                {{ Session::get('message') }}
            </div>
            @php Session::put('message', null); @endphp
        @endif

        @if (Session::has('success'))
            <div class="alert alert-success" role="alert">
                <i class="fa fa-check-circle" aria-hidden="true"></i>
                {{ Session::get('success') }}
            </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-error" role="alert">
                <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Login Form -->
        <form action="{{ route('user.cus_login') }}" method="post" id="loginForm" novalidate>
            @csrf

            <!-- Email Field -->
            <div class="form-group">
                <label for="email" class="form-label required">Địa chỉ email</label>
                <div class="input-group">
                    <i class="fa fa-envelope input-icon" aria-hidden="true"></i>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input @error('email') is-invalid @enderror"
                        placeholder="Nhập địa chỉ email của bạn"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                </div>
                @error('email')
                    <div class="error-message" id="email-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="form-group">
                <label for="password" class="form-label required">Mật khẩu</label>
                <div class="input-group">
                    <i class="fa fa-lock input-icon" aria-hidden="true"></i>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input @error('password') is-invalid @enderror"
                        placeholder="Nhập mật khẩu của bạn"
                        required
                        autocomplete="current-password"
                        aria-describedby="password-error"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')" aria-label="Hiển thị/Ẩn mật khẩu">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                @error('password')
                    <div class="error-message" id="password-error" role="alert">
                        <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Remember Me -->
            <div class="checkbox-group">
                <input type="checkbox" id="remember" name="remember" class="checkbox-input" {{ old('remember') ? 'checked' : '' }}>
                <label for="remember" class="checkbox-label">Ghi nhớ đăng nhập</label>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="btn btn-primary" id="loginBtn">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                Đăng nhập
            </button>

            <!-- Navigation Links -->
            <nav class="form-nav" role="navigation">
                <div class="form-nav-links">
                    <a href="#" class="form-link" onclick="showForgotPasswordAlert(); return false;">
                        <i class="fa fa-question-circle" aria-hidden="true"></i>
                        Quên mật khẩu?
                    </a>
                    <a href="{{ route('home.index') }}" class="form-link">
                        <i class="fa fa-home" aria-hidden="true"></i>
                        Trang chủ
                    </a>
                </div>

                <p style="margin-top: 1rem;">
                    Bạn chưa có tài khoản?
                    <a href="{{ route('user.cus_register') }}" class="form-link">
                        <strong>Đăng ký ngay</strong>
                    </a>
                </p>
            </nav>
        </form>
    </article>
</main>

    {{-- Validation script --}}
    <script>
        function validate() {
            var $valid = true;
            document.getElementById("user_info").innerHTML = "";
            document.getElementById("password_info").innerHTML = "";

            var userName = document.getElementById("user_name").value;
            var password = document.getElementById("password").value;

            if (userName == "") {
                document.getElementById("user_info").innerHTML = "required";
                $valid = false;
            }
            if (password == "") {
                document.getElementById("password_info").innerHTML = "required";
                $valid = false;
            }

            return $valid;
        }
    </script>
@endsection
