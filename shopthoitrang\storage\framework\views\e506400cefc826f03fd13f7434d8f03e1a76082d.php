<?php $__env->startSection('title', 'Quản lý Thương hiệu'); ?>
<?php $__env->startSection('page_title', 'Quản lý Thương hiệu'); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item active">Thương hiệu</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-industry mr-1"></i>
                        Danh sách thương hiệu
                    </h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('admin.manufacturers.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Thêm thương hiệu mới
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card-body border-bottom">
                    <form method="GET" action="<?php echo e(route('admin.manufacturers.index')); ?>" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">Tìm kiếm:</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="<?php echo e(request('search')); ?>" placeholder="Tên thương hiệu, email, mô tả...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Trạng thái:</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                                    <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="featured">Nổi bật:</label>
                                <select name="featured" id="featured" class="form-control">
                                    <option value="">Tất cả</option>
                                    <option value="yes" <?php echo e(request('featured') === 'yes' ? 'selected' : ''); ?>>Có</option>
                                    <option value="no" <?php echo e(request('featured') === 'no' ? 'selected' : ''); ?>>Không</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-info mr-2">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a href="<?php echo e(route('admin.manufacturers.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 50px">#</th>
                                    <th>Logo</th>
                                    <th>Thương hiệu</th>
                                    <th>Liên hệ</th>
                                    <th>Website</th>
                                    <th>Sản phẩm</th>
                                    <th>Thứ tự</th>
                                    <th>Trạng thái</th>
                                    <th>Nổi bật</th>
                                    <th style="width: 150px">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($manufacturer->id_manufacturer); ?></td>
                                    <td>
                                        <?php if($manufacturer->logo): ?>
                                            <img src="<?php echo e(asset('storage/' . $manufacturer->logo)); ?>" 
                                                 alt="<?php echo e($manufacturer->name_manufacturer); ?>" 
                                                 class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px; border-radius: 4px;">
                                                <i class="fas fa-industry text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($manufacturer->name_manufacturer); ?></strong>
                                            <?php if($manufacturer->slug): ?>
                                                <br><small class="text-muted"><?php echo e($manufacturer->slug); ?></small>
                                            <?php endif; ?>
                                            <?php if($manufacturer->description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($manufacturer->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($manufacturer->email): ?>
                                            <div><i class="fas fa-envelope text-muted"></i> <?php echo e($manufacturer->email); ?></div>
                                        <?php endif; ?>
                                        <?php if($manufacturer->phone): ?>
                                            <div><i class="fas fa-phone text-muted"></i> <?php echo e($manufacturer->phone); ?></div>
                                        <?php endif; ?>
                                        <?php if($manufacturer->address): ?>
                                            <div><i class="fas fa-map-marker-alt text-muted"></i> <?php echo e(Str::limit($manufacturer->address, 30)); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($manufacturer->website_url): ?>
                                            <a href="<?php echo e($manufacturer->website_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> Website
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary"><?php echo e($manufacturer->active_products_count ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge badge-light"><?php echo e($manufacturer->sort_order ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input status-toggle" 
                                                   id="status-<?php echo e($manufacturer->id_manufacturer); ?>"
                                                   data-id="<?php echo e($manufacturer->id_manufacturer); ?>"
                                                   <?php echo e($manufacturer->is_active ? 'checked' : ''); ?>>
                                            <label class="custom-control-label" for="status-<?php echo e($manufacturer->id_manufacturer); ?>"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($manufacturer->is_featured): ?>
                                            <i class="fas fa-star text-warning" title="Nổi bật"></i>
                                        <?php else: ?>
                                            <i class="far fa-star text-muted" title="Không nổi bật"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.manufacturers.show', $manufacturer->id_manufacturer)); ?>" 
                                               class="btn btn-info btn-sm" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.manufacturers.edit', $manufacturer->id_manufacturer)); ?>" 
                                               class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" 
                                                    onclick="confirmDelete('<?php echo e(route('admin.manufacturers.destroy', $manufacturer->id_manufacturer)); ?>', 'Bạn có chắc chắn muốn xóa thương hiệu này?')"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-industry fa-3x mb-3"></i>
                                            <p>Không có thương hiệu nào được tìm thấy.</p>
                                            <a href="<?php echo e(route('admin.manufacturers.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Thêm thương hiệu đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <?php if($manufacturers->hasPages()): ?>
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <small class="text-muted">
                                Hiển thị <?php echo e($manufacturers->firstItem()); ?> đến <?php echo e($manufacturers->lastItem()); ?> 
                                trong tổng số <?php echo e($manufacturers->total()); ?> thương hiệu
                            </small>
                        </div>
                        <div class="col-md-6">
                            <?php echo e($manufacturers->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Social Media Links Modal -->
    <div class="modal fade" id="socialModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Liên kết mạng xã hội</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="socialContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Toggle status
    $('.status-toggle').on('change', function() {
        const manufacturerId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/manufacturers/${manufacturerId}/toggle-status`,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Có lỗi xảy ra');
                    // Revert toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Có lỗi xảy ra');
                // Revert toggle
                $(this).prop('checked', !isActive);
            }
        });
    });

    // Show social media links
    function showSocialLinks(manufacturer) {
        let content = '<div class="list-group">';
        
        if (manufacturer.facebook_url) {
            content += `<a href="${manufacturer.facebook_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-facebook text-primary"></i> Facebook
            </a>`;
        }
        
        if (manufacturer.instagram_url) {
            content += `<a href="${manufacturer.instagram_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-instagram text-danger"></i> Instagram
            </a>`;
        }
        
        if (manufacturer.twitter_url) {
            content += `<a href="${manufacturer.twitter_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-twitter text-info"></i> Twitter
            </a>`;
        }
        
        if (manufacturer.youtube_url) {
            content += `<a href="${manufacturer.youtube_url}" target="_blank" class="list-group-item list-group-item-action">
                <i class="fab fa-youtube text-danger"></i> YouTube
            </a>`;
        }
        
        if (!manufacturer.facebook_url && !manufacturer.instagram_url && !manufacturer.twitter_url && !manufacturer.youtube_url) {
            content += '<p class="text-muted">Không có liên kết mạng xã hội nào.</p>';
        }
        
        content += '</div>';
        
        $('#socialContent').html(content);
        $('#socialModal').modal('show');
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/manufacturers/index.blade.php ENDPATH**/ ?>